<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug - Flori Construction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .log-output {
            max-height: 300px;
            overflow-y: auto;
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔐 Authentication Debug Tool</h1>
    <p>This tool helps debug authentication issues in the Flori Construction mobile app.</p>

    <div class="debug-section">
        <h3>📊 Current Authentication Status</h3>
        <div id="auth-status"></div>
        <button onclick="checkAuthStatus()">🔄 Refresh Status</button>
    </div>

    <div class="debug-section">
        <h3>🔑 Token Information</h3>
        <div id="token-info"></div>
        <button onclick="checkToken()">🔍 Analyze Token</button>
        <button onclick="clearToken()">🗑️ Clear Token</button>
    </div>

    <div class="debug-section">
        <h3>🌐 API Connectivity</h3>
        <div id="api-status"></div>
        <button onclick="testApiConnectivity()">🔗 Test API</button>
        <button onclick="testAuthEndpoint()">🔐 Test Auth Endpoint</button>
    </div>

    <div class="debug-section">
        <h3>🧪 Test Authentication</h3>
        <div>
            <input type="text" id="test-username" placeholder="Username" style="margin: 5px; padding: 8px;">
            <input type="password" id="test-password" placeholder="Password" style="margin: 5px; padding: 8px;">
            <button onclick="testLogin()">🚀 Test Login</button>
        </div>
        <div id="login-result"></div>
    </div>

    <div class="debug-section">
        <h3>📝 Debug Log</h3>
        <button onclick="clearLog()">🧹 Clear Log</button>
        <div id="debug-log" class="log-output"></div>
    </div>

    <script>
        // Debug logging
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToDebug(message, type = 'log') {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToDebug(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToDebug(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToDebug(args.join(' '), 'warn');
        };

        // Get API base URL
        function getApiBaseUrl() {
            const currentLocation = window.location;
            const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;
            
            if (currentLocation.pathname.includes('/mobile-app/')) {
                return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
            }
            
            return `${baseUrl}/api`;
        }

        const apiBase = getApiBaseUrl();
        console.log('API Base URL:', apiBase);

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkAuthStatus() {
            console.log('Checking authentication status...');
            
            const token = localStorage.getItem('flori_token');
            const user = localStorage.getItem('flori_user');
            
            let status = '';
            
            if (token) {
                status += `✅ Token found: ${token.substring(0, 20)}...<br>`;
            } else {
                status += `❌ No token found in localStorage<br>`;
            }
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    status += `✅ User data found: ${userData.username} (${userData.email})<br>`;
                } catch (e) {
                    status += `⚠️ Invalid user data in localStorage<br>`;
                }
            } else {
                status += `❌ No user data found in localStorage<br>`;
            }
            
            const type = (token && user) ? 'success' : 'error';
            showStatus('auth-status', status, type);
        }

        function checkToken() {
            console.log('Analyzing token...');
            
            const token = localStorage.getItem('flori_token');
            
            if (!token) {
                showStatus('token-info', '❌ No token found', 'error');
                return;
            }
            
            let info = `Token length: ${token.length} characters<br>`;
            info += `Token preview: ${token.substring(0, 30)}...<br>`;
            info += `Token type: ${typeof token}<br>`;
            
            showStatus('token-info', info, 'success');
        }

        function clearToken() {
            console.log('Clearing authentication data...');
            localStorage.removeItem('flori_token');
            localStorage.removeItem('flori_user');
            showStatus('token-info', '🗑️ Authentication data cleared', 'warning');
            checkAuthStatus();
        }

        async function testApiConnectivity() {
            console.log('Testing API connectivity...');
            
            try {
                const response = await fetch(`${apiBase}/auth.php?action=ping`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showStatus('api-status', `✅ API is accessible<br>Response: ${JSON.stringify(data)}`, 'success');
                } else {
                    showStatus('api-status', `⚠️ API responded but with error<br>Status: ${response.status}<br>Data: ${JSON.stringify(data)}`, 'warning');
                }
            } catch (error) {
                showStatus('api-status', `❌ API connection failed<br>Error: ${error.message}`, 'error');
            }
        }

        async function testAuthEndpoint() {
            console.log('Testing auth endpoint...');
            
            const token = localStorage.getItem('flori_token');
            
            if (!token) {
                showStatus('api-status', '❌ No token available for testing', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${apiBase}/auth.php?action=verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showStatus('api-status', `✅ Token is valid<br>User: ${data.user.username}<br>Response: ${JSON.stringify(data)}`, 'success');
                } else {
                    showStatus('api-status', `❌ Token verification failed<br>Status: ${response.status}<br>Error: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Auth endpoint test failed<br>Error: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            console.log('Testing login...');
            
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            if (!username || !password) {
                showStatus('login-result', '❌ Please enter username and password', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${apiBase}/auth.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    localStorage.setItem('flori_token', data.token);
                    localStorage.setItem('flori_user', JSON.stringify(data.user));
                    showStatus('login-result', `✅ Login successful!<br>Token: ${data.token.substring(0, 20)}...<br>User: ${data.user.username}`, 'success');
                    checkAuthStatus();
                } else {
                    showStatus('login-result', `❌ Login failed<br>Status: ${response.status}<br>Error: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('login-result', `❌ Login request failed<br>Error: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Authentication Debug Tool loaded');
            checkAuthStatus();
        });
    </script>
</body>
</html>
