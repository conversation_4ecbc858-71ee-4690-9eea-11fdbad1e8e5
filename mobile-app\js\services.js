/**
 * Services Manager for Flori Construction Admin Mobile App
 * Handles services CRUD operations and UI management
 */

class ServicesManager {
    constructor() {
        // Use the same API base as FloriAdmin
        this.apiBase = window.floriAdmin?.apiBase || this.getApiBaseUrl();
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentFilter = '';
        this.currentSearch = '';
        this.services = [];
        this.isInitialized = false;

        this.init();
    }

    getApiBaseUrl() {
        // Get current location and construct API URL
        const currentLocation = window.location;
        const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;

        // Check if we're in mobile-app directory
        if (currentLocation.pathname.includes('/mobile-app/')) {
            return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
        }

        // Default fallback
        return `${baseUrl}/api`;
    }

    init() {
        console.log('ServicesManager: Initializing...');
        this.bindEvents();
        this.isInitialized = true;
        console.log('ServicesManager: Initialized successfully');
    }

    bindEvents() {
        // Add service button
        const addServiceBtn = document.getElementById('add-service-btn');
        if (addServiceBtn) {
            addServiceBtn.addEventListener('click', () => this.showAddServiceModal());
        }

        // Service filter
        const serviceFilter = document.getElementById('service-filter');
        if (serviceFilter) {
            serviceFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }

        // Service search
        const serviceSearch = document.getElementById('service-search');
        if (serviceSearch) {
            let searchTimeout;
            serviceSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentSearch = e.target.value;
                    this.currentPage = 1;
                    this.load();
                }, 300);
            });
        }
    }

    async load() {
        try {
            console.log('ServicesManager: Loading services...');

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage
            });

            if (this.currentFilter) {
                if (this.currentFilter === 'featured') {
                    params.append('featured', 'true');
                }
            }

            if (this.currentSearch) {
                params.append('search', this.currentSearch);
            }

            const response = await window.floriAdmin.apiRequest(`services.php?${params}`);

            if (response && response.success) {
                this.services = response.services;
                this.renderServices();
                this.renderPagination(response.pagination);
                console.log('ServicesManager: Services loaded successfully');
            } else {
                throw new Error(response?.error || 'Failed to load services');
            }

        } catch (error) {
            console.error('Failed to load services:', error);
            window.floriAdmin.showToast('Failed to load services', 'error');
        }
    }

    renderServices() {
        const container = document.getElementById('services-list');
        if (!container) return;

        if (!this.services || this.services.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tools fa-3x"></i>
                    <h3>No services found</h3>
                    <p>No services match your current filters.</p>
                    <button class="btn btn-primary" onclick="window.ServicesManager.showAddServiceModal()">
                        <i class="fas fa-plus"></i> Add First Service
                    </button>
                </div>
            `;
            return;
        }

        const servicesHTML = this.services.map(service => this.renderServiceCard(service)).join('');
        container.innerHTML = servicesHTML;
    }

    renderServiceCard(service) {
        const imageUrl = service.featured_image_url || '../assets/images/placeholder-service.jpg';
        const featuredBadge = service.is_featured ? '<span class="featured-badge"><i class="fas fa-star"></i> Featured</span>' : '';

        return `
            <div class="service-card" data-service-id="${service.id}">
                <div class="service-image">
                    <img src="${imageUrl}" alt="${this.escapeHtml(service.title)}" loading="lazy"
                         onerror="this.src='../assets/images/placeholder-service.jpg'">
                    ${featuredBadge}
                </div>
                <div class="service-content">
                    <h3 class="service-title">${this.escapeHtml(service.title)}</h3>
                    <p class="service-description">${this.escapeHtml(this.truncateText(service.short_description, 100))}</p>
                    <div class="service-meta">
                        <span class="service-date">
                            <i class="fas fa-calendar"></i>
                            ${window.floriAdmin.formatDate(service.created_at)}
                        </span>
                        <span class="service-order">
                            <i class="fas fa-sort-numeric-down"></i>
                            Order: ${service.sort_order}
                        </span>
                    </div>
                </div>
                <div class="service-actions">
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.editService(${service.id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.viewService('${service.slug}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="window.ServicesManager.deleteService(${service.id}, '${this.escapeHtml(service.title)}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    }

    renderPagination(pagination) {
        const container = document.getElementById('services-pagination');
        if (!container || !pagination) return;

        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-controls">';

        // Previous button
        if (pagination.page > 1) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="window.ServicesManager.goToPage(${pagination.page - 1})">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.page ? 'btn-primary' : 'btn-outline';
            paginationHTML += `
                <button class="btn ${activeClass}" onclick="window.ServicesManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        // Next button
        if (pagination.page < pagination.pages) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="window.ServicesManager.goToPage(${pagination.page + 1})">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.load();
    }

    async showAddServiceModal() {
        const modalContent = `
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Add New Service</h3>
                <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-service-form" class="service-form">
                    <div class="form-group">
                        <label for="service-title">Service Title *</label>
                        <input type="text" id="service-title" name="title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-short-description">Short Description *</label>
                        <textarea id="service-short-description" name="short_description" rows="3" maxlength="200" required></textarea>
                        <small class="char-count">0/200 characters</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-description">Full Description *</label>
                        <textarea id="service-description" name="description" rows="6" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-sort-order">Sort Order</label>
                        <input type="number" id="service-sort-order" name="sort_order" value="0" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="service-featured" name="is_featured">
                            <span class="checkmark"></span>
                            Featured Service
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
                <button class="btn btn-primary" onclick="window.ServicesManager.saveService()">
                    <i class="fas fa-save"></i> Save Service
                </button>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
        this.initServiceForm();
    }

    initServiceForm() {
        // Character counter for short description
        const shortDesc = document.getElementById('service-short-description');
        const charCount = document.querySelector('.char-count');

        if (shortDesc && charCount) {
            shortDesc.addEventListener('input', () => {
                const length = shortDesc.value.length;
                charCount.textContent = `${length}/200 characters`;
                charCount.className = length > 200 ? 'char-count over-limit' : 'char-count';
            });
        }
    }

    async saveService(serviceId = null) {
        const form = document.getElementById('add-service-form') || document.getElementById('edit-service-form');
        if (!form) return;

        const formData = new FormData(form);
        const serviceData = {
            title: formData.get('title'),
            short_description: formData.get('short_description'),
            description: formData.get('description'),
            sort_order: parseInt(formData.get('sort_order')) || 0,
            is_featured: formData.get('is_featured') === 'on'
        };

        // Validation
        if (!serviceData.title || !serviceData.short_description || !serviceData.description) {
            window.floriAdmin.showToast('Please fill in all required fields', 'error');
            return;
        }

        try {
            let response;
            if (serviceId) {
                serviceData.id = serviceId;
                response = await window.floriAdmin.apiRequest('services.php', 'PUT', serviceData);
            } else {
                response = await window.floriAdmin.apiRequest('services.php', 'POST', serviceData);
            }

            if (response && response.success) {
                window.floriAdmin.showToast(serviceId ? 'Service updated successfully' : 'Service created successfully', 'success');
                window.floriAdmin.hideModal();
                this.load();
            } else {
                throw new Error(response?.error || 'Failed to save service');
            }

        } catch (error) {
            console.error('Failed to save service:', error);
            window.floriAdmin.showToast('Failed to save service: ' + error.message, 'error');
        }
    }

    async editService(serviceId) {
        try {
            // Get service details
            const response = await window.floriAdmin.apiRequest(`services.php?id=${serviceId}`);

            if (!response || !response.success || !response.services || response.services.length === 0) {
                throw new Error('Service not found');
            }

            const service = response.services[0];

            const modalContent = `
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Edit Service</h3>
                    <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="edit-service-form" class="service-form">
                        <div class="form-group">
                            <label for="edit-service-title">Service Title *</label>
                            <input type="text" id="edit-service-title" name="title" value="${this.escapeHtml(service.title)}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-short-description">Short Description *</label>
                            <textarea id="edit-service-short-description" name="short_description" rows="3" maxlength="200" required>${this.escapeHtml(service.short_description)}</textarea>
                            <small class="char-count">${service.short_description.length}/200 characters</small>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-description">Full Description *</label>
                            <textarea id="edit-service-description" name="description" rows="6" required>${this.escapeHtml(service.description)}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-sort-order">Sort Order</label>
                            <input type="number" id="edit-service-sort-order" name="sort_order" value="${service.sort_order}" min="0">
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="edit-service-featured" name="is_featured" ${service.is_featured ? 'checked' : ''}>
                                <span class="checkmark"></span>
                                Featured Service
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="window.ServicesManager.saveService(${serviceId})">
                        <i class="fas fa-save"></i> Update Service
                    </button>
                </div>
            `;

            window.floriAdmin.showModal(modalContent);
            this.initServiceForm();

        } catch (error) {
            console.error('Failed to load service for editing:', error);
            window.floriAdmin.showToast('Failed to load service details', 'error');
        }
    }

    viewService(slug) {
        // Open service page in new tab
        const serviceUrl = `../service.php?slug=${slug}`;
        window.open(serviceUrl, '_blank');
    }

    async deleteService(serviceId, serviceName) {
        if (!confirm(`Are you sure you want to delete the service "${serviceName}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await window.floriAdmin.apiRequest(`services.php?id=${serviceId}`, 'DELETE');

            if (response && response.success) {
                window.floriAdmin.showToast('Service deleted successfully', 'success');
                this.load();
            } else {
                throw new Error(response?.error || 'Failed to delete service');
            }

        } catch (error) {
            console.error('Failed to delete service:', error);
            window.floriAdmin.showToast('Failed to delete service: ' + error.message, 'error');
        }
    }

    // Helper methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
}

// Initialize services manager when DOM is ready
function initializeServicesManager() {
    if (!window.ServicesManager) {
        console.log('ServicesManager: Creating new instance');
        window.ServicesManager = new ServicesManager();
    } else {
        console.log('ServicesManager: Instance already exists');
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeServicesManager);

// Also initialize when floriAdmin is ready (for cases where scripts load out of order)
if (window.floriAdmin) {
    console.log('ServicesManager: floriAdmin already available');
    initializeServicesManager();
} else {
    // Wait for floriAdmin to be available
    let attempts = 0;
    const maxAttempts = 20;

    const waitForFloriAdmin = () => {
        attempts++;
        if (window.floriAdmin) {
            console.log('ServicesManager: floriAdmin now available');
            initializeServicesManager();
        } else if (attempts < maxAttempts) {
            setTimeout(waitForFloriAdmin, 100);
        } else {
            console.warn('ServicesManager: floriAdmin not available after waiting');
        }
    };

    setTimeout(waitForFloriAdmin, 100);
}
