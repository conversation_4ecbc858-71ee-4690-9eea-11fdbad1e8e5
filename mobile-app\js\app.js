/**
 * Main App JavaScript for Flori Construction Admin
 * Handles app initialization, navigation, and core functionality
 */

class FloriAdmin {
    constructor() {
        // Determine API base URL dynamically
        this.apiBase = this.getApiBaseUrl();
        this.token = localStorage.getItem('flori_token');
        this.user = this.loadStoredUser();
        this.currentPage = 'dashboard';
        this.isOnline = navigator.onLine;

        this.init();
    }

    getApiBaseUrl() {
        // Get current location and construct API URL
        const currentLocation = window.location;
        const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;

        // Check if we're in mobile-app directory
        if (currentLocation.pathname.includes('/mobile-app/')) {
            return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
        }

        // Default fallback
        return `${baseUrl}/api`;
    }

    loadStoredUser() {
        const userStr = localStorage.getItem('flori_user');
        return userStr ? JSON.parse(userStr) : null;
    }

    async init() {
        console.log('FloriAdmin: Initializing app...');
        console.log('FloriAdmin: API Base URL:', this.apiBase);

        // Show loading screen
        this.showLoading();

        // Setup event listeners first
        this.setupEventListeners();

        // Setup online/offline handlers
        this.setupNetworkHandlers();

        // Check if user is logged in
        if (this.token && this.user) {
            console.log('FloriAdmin: Found stored token and user, verifying...');
            const isValid = await this.verifyToken();
            if (isValid) {
                console.log('FloriAdmin: Token valid, showing main app');
                this.showMainApp();
                await this.loadDashboard();
            } else {
                console.log('FloriAdmin: Token invalid, showing login');
                this.clearAuthData();
                this.showLogin();
            }
        } else {
            console.log('FloriAdmin: No token or user found, showing login');
            this.showLogin();
        }

        // Register service worker
        this.registerServiceWorker();

        // Hide loading screen
        this.hideLoading();

        console.log('FloriAdmin: Initialization complete');
    }

    setupNetworkHandlers() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showToast('Connection restored', 'success');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showToast('Connection lost. Some features may not work.', 'warning');
        });
    }

    clearAuthData() {
        localStorage.removeItem('flori_token');
        localStorage.removeItem('flori_user');
        this.token = null;
        this.user = null;
    }

    // Debug function to check authentication status
    debugAuthStatus() {
        console.log('=== AUTHENTICATION DEBUG ===');
        console.log('Token in instance:', this.token ? `${this.token.substring(0, 10)}...` : 'null');
        console.log('Token in localStorage:', localStorage.getItem('flori_token') ? `${localStorage.getItem('flori_token').substring(0, 10)}...` : 'null');
        console.log('User in instance:', this.user);
        console.log('User in localStorage:', localStorage.getItem('flori_user'));
        console.log('API Base URL:', this.apiBase);
        console.log('Is Online:', this.isOnline);
        console.log('Current Page:', this.currentPage);
        console.log('============================');
    }

    setupEventListeners() {
        // Menu toggle for mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');

        menuToggle?.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !menuToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });

        // Navigation menu items
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.navigateTo(page);

                // Close mobile menu
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        logoutBtn?.addEventListener('click', () => {
            this.logout();
        });

        // Modal close
        const modalOverlay = document.getElementById('modal-overlay');
        modalOverlay?.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                this.closeModal();
            }
        });

        // Handle back button
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'dashboard';
            this.navigateTo(page, false);
        });
    }

    async verifyToken() {
        if (!this.token) {
            console.log('FloriAdmin: No token to verify');
            return false;
        }

        try {
            console.log('FloriAdmin: Verifying token...');
            console.log('FloriAdmin: Token preview:', this.token ? `${this.token.substring(0, 10)}...` : 'null');
            console.log('FloriAdmin: API Base URL:', this.apiBase);

            const response = await fetch(`${this.apiBase}/auth.php?action=verify`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            console.log('FloriAdmin: Token verification response status:', response.status);

            if (!response.ok) {
                console.error('FloriAdmin: Token verification HTTP error:', response.status);

                // Try to get error details
                try {
                    const errorData = await response.json();
                    console.error('FloriAdmin: Token verification error details:', errorData);
                } catch (e) {
                    console.error('FloriAdmin: Could not parse error response');
                }

                return false;
            }

            const data = await response.json();
            console.log('FloriAdmin: Token verification response:', data);

            if (data.success) {
                this.user = data.user;
                // Update stored user data
                localStorage.setItem('flori_user', JSON.stringify(data.user));
                console.log('FloriAdmin: Token verification successful for user:', data.user.username);
                return true;
            }

            console.log('FloriAdmin: Token verification failed:', data.error);
            return false;
        } catch (error) {
            console.error('FloriAdmin: Token verification error:', error);
            console.error('FloriAdmin: Error details:', error.message, error.stack);
            return false;
        }
    }

    navigateTo(page, pushState = true) {
        // Update active menu item
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeMenuItem = document.querySelector(`[data-page="${page}"]`);
        activeMenuItem?.classList.add('active');

        // Hide all pages
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });

        // Show target page
        const targetPage = document.getElementById(`${page}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = page;

            // Update page title
            const pageTitle = document.getElementById('page-title');
            if (pageTitle) {
                pageTitle.textContent = this.getPageTitle(page);
            }

            // Load page content
            this.loadPageContent(page);

            // Update URL
            if (pushState) {
                history.pushState({ page }, '', `#${page}`);
            }
        }
    }

    getPageTitle(page) {
        const titles = {
            dashboard: 'Dashboard',
            projects: 'Projects',
            media: 'Media',
            content: 'Content',
            settings: 'Settings'
        };

        return titles[page] || 'Dashboard';
    }

    async loadPageContent(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboard();
                break;
            case 'projects':
                await this.loadProjects();
                break;
            case 'media':
                await this.loadMedia();
                break;
            case 'content':
                await this.loadContent();
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }

    async loadDashboard() {
        try {
            // Load dashboard statistics
            const [projectsResponse, mediaResponse] = await Promise.all([
                fetch(`${this.apiBase}/projects.php`, {
                    headers: { 'Authorization': `Bearer ${this.token}` }
                }),
                fetch(`${this.apiBase}/media.php`, {
                    headers: { 'Authorization': `Bearer ${this.token}` }
                })
            ]);

            const projectsData = await projectsResponse.json();
            const mediaData = await mediaResponse.json();

            if (projectsData.success) {
                const projects = projectsData.projects;
                const completed = projects.filter(p => p.project_type === 'completed').length;
                const ongoing = projects.filter(p => p.project_type === 'ongoing').length;

                document.getElementById('total-projects').textContent = projects.length;
                document.getElementById('completed-projects').textContent = completed;
                document.getElementById('ongoing-projects').textContent = ongoing;

                // Load recent projects
                this.loadRecentProjects(projects.slice(0, 5));
            }

            if (mediaData.success) {
                document.getElementById('total-media').textContent = mediaData.pagination.total;
            }

        } catch (error) {
            console.error('Failed to load dashboard:', error);
            this.showToast('Failed to load dashboard data', 'error');
        }
    }

    loadRecentProjects(projects) {
        const container = document.getElementById('recent-projects');
        if (!container) return;

        if (projects.length === 0) {
            container.innerHTML = '<p>No recent projects</p>';
            return;
        }

        const html = projects.map(project => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="activity-content">
                    <h4>${this.escapeHtml(project.title)}</h4>
                    <p>${this.escapeHtml(project.location || 'No location specified')}</p>
                    <span class="activity-time">${this.formatDate(project.created_at)}</span>
                </div>
                <span class="project-type ${project.project_type}">${project.project_type}</span>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    async loadProjects() {
        // This will be implemented in projects.js
        console.log('FloriAdmin: Loading projects page...');

        if (window.ProjectsManager && window.ProjectsManager.isInitialized) {
            console.log('FloriAdmin: ProjectsManager available, loading projects...');
            await window.ProjectsManager.load();
        } else {
            console.warn('FloriAdmin: ProjectsManager not loaded, waiting...');

            // Wait a bit for ProjectsManager to initialize
            let attempts = 0;
            const maxAttempts = 10;

            const waitForManager = () => {
                attempts++;
                if (window.ProjectsManager && window.ProjectsManager.isInitialized) {
                    console.log('FloriAdmin: ProjectsManager now available, loading projects...');
                    window.ProjectsManager.load();
                } else if (attempts < maxAttempts) {
                    setTimeout(waitForManager, 100);
                } else {
                    console.error('FloriAdmin: ProjectsManager failed to initialize');
                    this.showToast('Failed to initialize projects manager', 'error');
                }
            };

            setTimeout(waitForManager, 100);
        }
    }

    async loadMedia() {
        // This will be implemented in media.js
        if (window.MediaManager) {
            window.MediaManager.load();
        } else {
            console.warn('MediaManager not loaded');
        }
    }

    async loadContent() {
        // This will be implemented in content.js
        if (window.ContentManager) {
            window.ContentManager.load();
        } else {
            console.warn('ContentManager not loaded');
        }
    }

    async loadSettings() {
        if (!this.user) return;

        // Populate user profile form
        const fullNameInput = document.getElementById('full-name');
        const emailInput = document.getElementById('email');

        if (fullNameInput) fullNameInput.value = this.user.full_name || '';
        if (emailInput) emailInput.value = this.user.email || '';
    }

    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }

    showLogin() {
        const loginScreen = document.getElementById('login-screen');
        const mainApp = document.getElementById('main-app');

        if (loginScreen) {
            loginScreen.style.display = 'block';
        }
        if (mainApp) {
            mainApp.style.display = 'none';
        }
    }

    showMainApp() {
        const loginScreen = document.getElementById('login-screen');
        const mainApp = document.getElementById('main-app');

        if (loginScreen) {
            loginScreen.style.display = 'none';
        }
        if (mainApp) {
            mainApp.style.display = 'grid';
        }
    }

    logout() {
        // Clear token and user data
        localStorage.removeItem('flori_token');
        this.token = null;
        this.user = null;

        // Call logout API
        fetch(`${this.apiBase}/auth.php?action=logout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        }).catch(() => {
            // Ignore errors on logout
        });

        // Show login screen
        this.showLogin();

        // Show toast
        this.showToast('Logged out successfully', 'success');
    }

    showModal(content) {
        const modalOverlay = document.getElementById('modal-overlay');
        const modalContent = document.getElementById('modal-content');

        if (modalContent) {
            modalContent.innerHTML = content;
        }

        if (modalOverlay) {
            modalOverlay.classList.add('active');
        }
    }

    closeModal() {
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span>${this.escapeHtml(message)}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    async apiRequest(endpoint, options = {}) {
        console.log('FloriAdmin: Making API request to:', endpoint);

        // Check if token exists
        if (!this.token) {
            console.warn('FloriAdmin: No token available for API request');
            console.log('FloriAdmin: Checking localStorage for token...');

            // Try to get token from localStorage again
            this.token = localStorage.getItem('flori_token');

            if (!this.token) {
                console.error('FloriAdmin: No token found in localStorage either');
                this.showToast('Please log in to continue', 'error');
                this.showLogin();
                return { success: false, error: 'No authentication token available' };
            } else {
                console.log('FloriAdmin: Found token in localStorage, updating instance');
            }
        }

        console.log('FloriAdmin: Using token:', this.token ? `${this.token.substring(0, 10)}...` : 'null');

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const url = `${this.apiBase}/${endpoint}`;
            console.log('FloriAdmin: Full URL:', url);
            console.log('FloriAdmin: Request headers:', mergedOptions.headers);

            const response = await fetch(url, mergedOptions);
            console.log('FloriAdmin: Response status:', response.status);

            if (!response.ok) {
                console.error('FloriAdmin: HTTP error:', response.status, response.statusText);

                if (response.status === 401) {
                    // Token expired or invalid - try to refresh
                    console.log('FloriAdmin: 401 error, attempting token refresh...');

                    const refreshed = await this.attemptTokenRefresh();
                    if (refreshed) {
                        console.log('FloriAdmin: Token refreshed, retrying request...');
                        // Retry the request with new token
                        mergedOptions.headers.Authorization = `Bearer ${this.token}`;
                        const retryResponse = await fetch(url, mergedOptions);

                        if (retryResponse.ok) {
                            const retryData = await retryResponse.json();
                            console.log('FloriAdmin: Retry successful:', retryData);
                            return retryData;
                        }
                    }

                    // If refresh failed or retry failed, logout
                    console.log('FloriAdmin: Token refresh failed, logging out');
                    this.logout();
                    return { success: false, error: 'Authentication expired' };
                }

                return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
            }

            const data = await response.json();
            console.log('FloriAdmin: Response data:', data);
            return data;

        } catch (error) {
            console.error('FloriAdmin: API request failed:', error);
            console.error('FloriAdmin: Error details:', error.message, error.stack);

            // Only show toast if container exists
            const toastContainer = document.getElementById('toast-container');
            if (toastContainer) {
                this.showToast('Network error occurred: ' + error.message, 'error');
            }

            return { success: false, error: 'Network error: ' + error.message };
        }
    }

    async attemptTokenRefresh() {
        try {
            console.log('FloriAdmin: Attempting to refresh token...');

            if (window.authManager && typeof window.authManager.refreshToken === 'function') {
                const success = await window.authManager.refreshToken();
                if (success) {
                    // Update our token reference
                    this.token = localStorage.getItem('flori_token');
                    console.log('FloriAdmin: Token refreshed successfully');
                    return true;
                }
            }

            console.log('FloriAdmin: Token refresh failed');
            return false;
        } catch (error) {
            console.error('FloriAdmin: Token refresh error:', error);
            return false;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/mobile-app/sw.js');
                console.log('Service Worker registered successfully');
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.floriAdmin = new FloriAdmin();

    // Add global debug functions
    window.debugAuth = () => {
        if (window.floriAdmin) {
            window.floriAdmin.debugAuthStatus();
        } else {
            console.log('FloriAdmin not initialized yet');
        }
    };

    window.testProjectEdit = async (projectId = 8) => {
        console.log(`Testing project edit for ID: ${projectId}`);
        if (window.floriAdmin) {
            try {
                const response = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`);
                console.log('Project edit test result:', response);
                return response;
            } catch (error) {
                console.error('Project edit test failed:', error);
                return { success: false, error: error.message };
            }
        } else {
            console.log('FloriAdmin not initialized yet');
        }
    };

    console.log('Debug functions available: debugAuth(), testProjectEdit(projectId)');
});

// Handle install prompt for PWA
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;

    // Show install button or banner
    const installBanner = document.createElement('div');
    installBanner.className = 'install-banner';
    installBanner.innerHTML = `
        <div class="install-content">
            <span>Install Flori Admin app for better experience</span>
            <button id="install-btn" class="btn btn-primary">Install</button>
            <button id="dismiss-install" class="btn btn-ghost">Dismiss</button>
        </div>
    `;

    document.body.appendChild(installBanner);

    document.getElementById('install-btn').addEventListener('click', async () => {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            console.log(`User response to the install prompt: ${outcome}`);
            deferredPrompt = null;
            installBanner.remove();
        }
    });

    document.getElementById('dismiss-install').addEventListener('click', () => {
        installBanner.remove();
    });
});

// Handle app installed
window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    deferredPrompt = null;
});
