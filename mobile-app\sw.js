/**
 * Service Worker for Flori Construction Admin PWA
 * Handles caching, offline functionality, and push notifications
 */

const CACHE_NAME = 'flori-admin-v1.0.0';
const API_CACHE_NAME = 'flori-api-v1.0.0';

// Files to cache for offline functionality
const STATIC_CACHE_FILES = [
    '/mobile-app/',
    '/mobile-app/index.html',
    '/mobile-app/manifest.json',
    '/mobile-app/css/app.css',
    '/mobile-app/js/app.js',
    '/mobile-app/js/auth.js',
    '/mobile-app/js/projects.js',
    '/mobile-app/js/services.js',
    '/mobile-app/js/media.js',
    '/mobile-app/js/content.js',
    '/mobile-app/icons/icon-192x192.png',
    '/mobile-app/icons/icon-512x512.png',
    '/assets/images/logo.png',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    /\/api\/auth\.php/,
    /\/api\/projects\.php/,
    /\/api\/media\.php/,
    /\/api\/content\.php/
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker installing...');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Caching static files...');
                return cache.addAll(STATIC_CACHE_FILES);
            })
            .then(() => {
                console.log('Static files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Failed to cache static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker activating...');

    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
    const request = event.request;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Handle different types of requests
    if (isStaticFile(url)) {
        // Static files: Cache First strategy
        event.respondWith(cacheFirst(request));
    } else if (isAPIRequest(url)) {
        // API requests: Network First strategy
        event.respondWith(networkFirst(request));
    } else if (isImageRequest(url)) {
        // Images: Cache First strategy
        event.respondWith(cacheFirst(request));
    } else {
        // Other requests: Network First strategy
        event.respondWith(networkFirst(request));
    }
});

// Cache First strategy - good for static files
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);

        // Cache successful responses
        if (networkResponse.status === 200) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error('Cache First strategy failed:', error);

        // Return offline fallback if available
        return getOfflineFallback(request);
    }
}

// Network First strategy - good for API requests
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);

        // Cache successful API responses
        if (networkResponse.status === 200 && isAPIRequest(new URL(request.url))) {
            const cache = await caches.open(API_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error('Network request failed:', error);

        // Try to get from cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        // Return offline fallback
        return getOfflineFallback(request);
    }
}

// Check if request is for static files
function isStaticFile(url) {
    return url.pathname.includes('/mobile-app/') &&
        (url.pathname.endsWith('.html') ||
            url.pathname.endsWith('.css') ||
            url.pathname.endsWith('.js') ||
            url.pathname.endsWith('.json'));
}

// Check if request is for API
function isAPIRequest(url) {
    return url.pathname.includes('/api/') ||
        API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

// Check if request is for images
function isImageRequest(url) {
    return url.pathname.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/i);
}

// Get offline fallback response
async function getOfflineFallback(request) {
    const url = new URL(request.url);

    if (url.pathname.includes('/mobile-app/')) {
        // Return cached index.html for app routes
        const cache = await caches.open(CACHE_NAME);
        return cache.match('/mobile-app/index.html');
    }

    if (isAPIRequest(url)) {
        // Return offline API response
        return new Response(
            JSON.stringify({
                error: 'Offline - this feature requires internet connection',
                offline: true
            }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }

    // Return generic offline response
    return new Response(
        'Offline - please check your internet connection',
        {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
                'Content-Type': 'text/plain'
            }
        }
    );
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    console.log('Background sync triggered:', event.tag);

    if (event.tag === 'upload-project') {
        event.waitUntil(syncOfflineProjects());
    } else if (event.tag === 'upload-media') {
        event.waitUntil(syncOfflineMedia());
    }
});

// Sync offline projects when connection is restored
async function syncOfflineProjects() {
    try {
        // Get offline projects from IndexedDB
        const offlineProjects = await getOfflineData('projects');

        for (const project of offlineProjects) {
            try {
                const response = await fetch('/api/projects.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${project.token}`
                    },
                    body: JSON.stringify(project.data)
                });

                if (response.ok) {
                    // Remove from offline storage
                    await removeOfflineData('projects', project.id);
                    console.log('Synced offline project:', project.id);
                }
            } catch (error) {
                console.error('Failed to sync project:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Sync offline media when connection is restored
async function syncOfflineMedia() {
    try {
        // Get offline media from IndexedDB
        const offlineMedia = await getOfflineData('media');

        for (const media of offlineMedia) {
            try {
                const formData = new FormData();
                formData.append('file', media.file);
                formData.append('directory', media.directory);

                const response = await fetch('/api/media.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${media.token}`
                    },
                    body: formData
                });

                if (response.ok) {
                    // Remove from offline storage
                    await removeOfflineData('media', media.id);
                    console.log('Synced offline media:', media.id);
                }
            } catch (error) {
                console.error('Failed to sync media:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Push notification handling
self.addEventListener('push', (event) => {
    console.log('Push notification received:', event);

    const options = {
        body: 'You have new updates in Flori Construction Admin',
        icon: '/mobile-app/icons/icon-192x192.png',
        badge: '/mobile-app/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/mobile-app/'
        },
        actions: [
            {
                action: 'open',
                title: 'Open App',
                icon: '/mobile-app/icons/icon-72x72.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss',
                icon: '/mobile-app/icons/icon-72x72.png'
            }
        ]
    };

    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.data = { ...options.data, ...data };
    }

    event.waitUntil(
        self.registration.showNotification('Flori Construction Admin', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('Notification clicked:', event);

    event.notification.close();

    if (event.action === 'dismiss') {
        return;
    }

    // Open or focus the app
    event.waitUntil(
        clients.matchAll({ type: 'window' })
            .then((clientList) => {
                // Check if app is already open
                for (const client of clientList) {
                    if (client.url.includes('/mobile-app/') && 'focus' in client) {
                        return client.focus();
                    }
                }

                // Open new window
                if (clients.openWindow) {
                    return clients.openWindow('/mobile-app/');
                }
            })
    );
});

// Utility functions for IndexedDB operations
async function getOfflineData(storeName) {
    // This would typically use IndexedDB
    // For now, return empty array
    return [];
}

async function removeOfflineData(storeName, id) {
    // This would typically remove from IndexedDB
    console.log(`Removing offline data: ${storeName}/${id}`);
}

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker received message:', event.data);

    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

console.log('Service Worker loaded successfully');
